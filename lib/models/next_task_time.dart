import 'package:flower_timemachine/controller/nurture_types_controller.dart';
import 'package:flower_timemachine/repository/monthly_nurture_cycle_repo.dart';
import 'package:flower_timemachine/types/flower_monthly_cycles.dart';

import '../common/global.dart';
import 'flower.dart';
import 'maintenance_record.dart';
import 'nurture_types.dart';

class NextMaintenanceTime {
  static const tableName = 'next_maintenance_time';
  final int flowerId;
  final NurtureType type;
  // 下次养护时间（单位：秒）
  final int nextTime;

  NextMaintenanceTime({
    required this.flowerId,
    required this.type,
    required this.nextTime,
  });

  static String createTable() {
    return 'CREATE TABLE $tableName('
        'flowerId INTEGER, '
        'type INTEGER,'
        'nextTime INTEGER)';
  }

  static Future<List<NextMaintenanceTime>> getAll() async {
    final db = await DB.get();

    final List<Map<String, dynamic>> rows = await db.sqlite.query(
      tableName,
    );

    List<NextMaintenanceTime> result = [];
    for (final row in rows) {
      final typeInfo = NurtureTypesController.get().getTypeInfo(row['type']);
      if (typeInfo == null) {
        continue;
      }

      result.add(NextMaintenanceTime(
        flowerId: row['flowerId'],
        type: typeInfo,
        nextTime: row['nextTime'],
      ));
    }

    return result;
  }

  static Future<List<NextMaintenanceTime>> get(int flowerId) async {
    final db = await DB.get();

    final List<Map<String, dynamic>> rows = await db.sqlite.query(
      tableName,
      where: 'flowerId = ?',
      whereArgs: [flowerId],
    );

    List<NextMaintenanceTime> result = [];
    for (final row in rows) {
      final typeInfo = NurtureTypesController.get().getTypeInfo(row['type']);
      if (typeInfo == null || !typeInfo.enable) {
        continue;
      }

      result.add(NextMaintenanceTime(
        flowerId: row['flowerId'],
        type: typeInfo,
        nextTime: row['nextTime'],
      ));
    }

    return result;
  }

  static Future<List<NextMaintenanceTime>> createFromFlower(
      Flower flower, FlowerMonthlyCycles? monthlyCycles) async {
    final ret = <NextMaintenanceTime>[];
    if (monthlyCycles == null) {
      return ret;
    }

    final flowerId = flower.id;
    final nurtureTypesController = NurtureTypesController.get();
    final now = DateTime.now();
    final currentMonth = now.month;

    for (final entry in monthlyCycles.typesCycles.entries) {
      final typeId = entry.key;
      final nurtureType = nurtureTypesController.getTypeInfo(typeId);
      if (nurtureType == null || !nurtureType.enable) {
        continue;
      }

      final effectiveCycle = monthlyCycles.getEffectiveCycleForTypeAndMonth(typeId, currentMonth, nurtureType);
      if (effectiveCycle <= 0) {
        continue;
      }

      final lastTime = DateTime.fromMillisecondsSinceEpoch(flower.createTime * 1000);
      final nextTime = _add(lastTime, effectiveCycle);
      ret.add(NextMaintenanceTime(flowerId: flowerId, type: nurtureType, nextTime: nextTime));
    }

    await _insertDB(flowerId, ret);

    return ret;
  }

  /// 计算下次需要任务的时间，并更新数据库
  static Future<List<NextMaintenanceTime>> calc(Flower flower) async {
    final db = await DB.get();

    // 查询养护记录
    final List<Map<String, dynamic>> records = await db.sqlite.rawQuery(
        'SELECT '
        'type, MAX(time) as time '
        'FROM ${MaintenanceRecord.tableName} '
        'WHERE '
        'flowerId = ? '
        'GROUP BY type',
        [flower.id]);

    final Map<int, int> recordMap = {};
    for (final record in records) {
      recordMap[record['type']] = record['time'];
    }

    final monthlyCycles = await MonthlyNurtureCycleRepository.getFlowerMonthlyCycles(flower.id);
    final nurtureTypesController = NurtureTypesController.get();
    final now = DateTime.now();
    final currentMonth = now.month;

    final ret = <NextMaintenanceTime>[];
    final flowerId = flower.id;

    for (final entry in monthlyCycles.typesCycles.entries) {
      final typeId = entry.key;
      final nurtureType = nurtureTypesController.getTypeInfo(typeId);
      if (nurtureType == null || !nurtureType.enable) {
        continue;
      }

      final effectiveCycle = monthlyCycles.getEffectiveCycleForTypeAndMonth(typeId, currentMonth, nurtureType);
      if (effectiveCycle <= 0) {
        continue;
      }

      // 如果没有养护记录就用花的创建时间
      final lastTimeTS = (recordMap[typeId] ?? flower.createTime) * 1000;
      final lastTime = DateTime.fromMillisecondsSinceEpoch(lastTimeTS);
      final nextTime = _add(lastTime, effectiveCycle);

      ret.add(NextMaintenanceTime(flowerId: flowerId, type: nurtureType, nextTime: nextTime));
    }

    await _updateDB(flowerId, ret);

    return ret;
  }

  static Future<void> _updateDB(int flowerId, List<NextMaintenanceTime> times) async {
    final db = await DB.get();
    final batch = db.sqlite.batch();

    // 先删除是为了保证一些取消提醒设置的任务
    batch.delete(tableName, where: "flowerId = ?", whereArgs: [flowerId]);
    for (final nextTime in times) {
      batch.insert(tableName, {
        'flowerId': flowerId,
        'type': nextTime.type.id,
        'nextTime': nextTime.nextTime,
      });
    }

    await batch.commit();
  }

  static Future<void> _insertDB(int flowerId, List<NextMaintenanceTime> times) async {
    final db = await DB.get();
    final batch = db.sqlite.batch();

    for (final nextTime in times) {
      batch.insert(tableName, {
        'flowerId': flowerId,
        'type': nextTime.type.id,
        'nextTime': nextTime.nextTime,
      });
    }

    await batch.commit();
  }

  static int _add(DateTime lastTime, int intervalDay) {
    // 时分秒清零
    final formattedNowLastTime = DateTime(lastTime.year, lastTime.month, lastTime.day);
    final nextTime = formattedNowLastTime.add(Duration(days: intervalDay));

    return (nextTime.millisecondsSinceEpoch / 1000).truncate();
  }

  static Future<int> deleteByNurtureType(int nurtureTypeId) async {
    final db = await DB.get();
    return await db.sqlite.delete(tableName, where: 'type = ?', whereArgs: [nurtureTypeId]);
  }
}
