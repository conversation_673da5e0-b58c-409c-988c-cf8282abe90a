import 'package:flower_timemachine/common/global.dart';

class FlowerMonthlyNurtureCycle {
  static const tableName = 'flower_monthly_nurture_cycle';

  final int id;
  final int flowerId;
  final int typeId;
  final int month; // 1-12
  final int cycle; // -1=未设置, 0=继承, >0=天数

  FlowerMonthlyNurtureCycle(this.id, this.flowerId, this.typeId, this.month, this.cycle);

  static String createTable() {
    return 'CREATE TABLE $tableName('
        'id INTEGER PRIMARY KEY, '
        'flower_id INTEGER, '
        'type_id INTEGER, '
        'month INTEGER, '
        'cycle INTEGER)';
  }

  static Future<List<FlowerMonthlyNurtureCycle>> getByFlower(int flowerId) async {
    final db = await DB.get();
    List<Map<String, dynamic>> rows = await db.sqlite.query(
      tableName,
      where: "flower_id = ?",
      whereArgs: [flowerId]
    );

    return rows.map((row) => FlowerMonthlyNurtureCycle(
      row['id'],
      row['flower_id'],
      row['type_id'],
      row['month'],
      row['cycle']
    )).toList();
  }

  static Future<List<FlowerMonthlyNurtureCycle>> getByFlowerAndType(int flowerId, int typeId) async {
    final db = await DB.get();
    List<Map<String, dynamic>> rows = await db.sqlite.query(
      tableName,
      where: "flower_id = ? AND type_id = ?",
      whereArgs: [flowerId, typeId]
    );

    return rows.map((row) => FlowerMonthlyNurtureCycle(
      row['id'],
      row['flower_id'],
      row['type_id'],
      row['month'],
      row['cycle']
    )).toList();
  }

  static Future<void> createBatch(int flowerId, int typeId, List<int> monthlyCycles) async {
    final db = await DB.get();
    final batch = db.sqlite.batch();

    for (int month = 1; month <= 12; month++) {
      final cycle = monthlyCycles[month - 1];
      // 只保存非继承（非0）的月份数据
      if (cycle != 0) {
        batch.insert(tableName, {
          "flower_id": flowerId,
          "type_id": typeId,
          "month": month,
          "cycle": cycle
        });
      }
    }

    await batch.commit(noResult: true);
  }

  static Future<void> updateBatch(int flowerId, int typeId, List<int> monthlyCycles) async {
    final db = await DB.get();
    final batch = db.sqlite.batch();

    // 删除现有数据
    batch.delete(tableName, where: 'flower_id = ? AND type_id = ?', whereArgs: [flowerId, typeId]);

    // 插入新数据（只保存非继承的月份数据）
    for (int month = 1; month <= 12; month++) {
      final cycle = monthlyCycles[month - 1];
      // 只保存非继承（非0）的月份数据
      if (cycle != 0) {
        batch.insert(tableName, {
          "flower_id": flowerId,
          "type_id": typeId,
          "month": month,
          "cycle": cycle
        });
      }
    }

    await batch.commit(noResult: true);
  }

  static Future<int> deleteByFlower(int flowerId) async {
    final db = await DB.get();
    return await db.sqlite.delete(tableName, where: 'flower_id = ?', whereArgs: [flowerId]);
  }

  static Future<int> deleteByNurtureType(int nurtureTypeId) async {
    final db = await DB.get();
    return await db.sqlite.delete(tableName, where: 'type_id = ?', whereArgs: [nurtureTypeId]);
  }
}
