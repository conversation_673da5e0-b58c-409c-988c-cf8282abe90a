import 'dart:io';
import 'dart:core';

import 'package:flower_timemachine/common/global.dart';
import 'package:flower_timemachine/repository/monthly_nurture_cycle_repo.dart';
import 'package:flower_timemachine/models/flower_timeline.dart';
import 'package:flower_timemachine/models/maintenance_record.dart';
import 'package:flower_timemachine/controller/nitification_manager.dart';
import 'package:flower_timemachine/models/photo_record.dart';
import 'package:flower_timemachine/models/tag_info.dart';
import 'package:flower_timemachine/models/tag_info_mapping.dart';
import 'package:flower_timemachine/types/flower_monthly_cycles.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:sqflite/sqflite.dart';
import 'package:collection/collection.dart';
import 'package:r_string_transform/r_string_transform.dart';

import 'flower_notes.dart';
import 'next_task_time.dart';
import '../controller/recent_maintenance_day.dart';
import 'last_maintenance_time.dart';

class Flower with ChangeNotifier {
  Flower._internal({
    required this.id,
    String? avatar,
    required this.name,
    int? createTime,
    List<TagInfo>? tags,
    int? arrivalTime,
    int? archiveTime,
    String? archiveReason,
    String? normalizedName,
  })  : _tags = tags ?? [],
        _avatar = avatar,
        _arrivalTime = arrivalTime,
        _archiveTime = archiveTime,
        _archiveReason = archiveReason,
        _normalizedName = normalizedName,
        createTime = createTime ?? (DateTime.now().millisecondsSinceEpoch / 1000).round();

  final int id;
  String name;

  // 创建时间（单位：秒）
  late int createTime;

  // 到家时间（单位：秒)
  int? _arrivalTime;

  // 归档时间 （单位：秒）
  int? _archiveTime;

  // 归档原因
  String? _archiveReason;

  // 头像
  String? _avatar;

  // 缓存下次任务时间记录
  RecentMaintenanceDay? nextTaskRecord;

  // 缓存最近养护记录的时间
  LastMaintenanceTime? _lastMaintenanceTime;

  // 缓存名称
  String? _normalizedName;

  // 标签
  final List<TagInfo> _tags;

  static const tableName = 'flowers';

  static const idField = "id";
  static const nameField = "name";
  static const avatarField = "avatar";
  static const arrivalTimeField = "arrivalTime";
  static const archiveTimeField = "archiveTime";
  static const archiveReasonField = "archiveReason";
  static const normalizedNameField = "normalizedName";

  static String createTable() {
    return 'CREATE TABLE $tableName('
        'id INTEGER PRIMARY KEY, '
        'name TEXT, '
        'avatar TEXT,'
        'arrivalTime INTEGER,'
        'archiveTime INTEGER,'
        'archiveReason TEXT,'
        'normalizedName TEXT,'
        'createTime INTEGER)';
  }

  static Future<Flower> createFromDBRow(Map<String, dynamic> row) async {
    final flowerId = row['id'];
    final tags = await TagInfoMapping.getByFlowerId(flowerId);

    final f = Flower._internal(
        id: flowerId,
        name: row['name'],
        avatar: row['avatar'],
        createTime: row['createTime'],
        arrivalTime: row['arrivalTime'],
        archiveTime: row['archiveTime'],
        archiveReason: row['archiveReason'],
        normalizedName: row['normalizedName'],
        tags: tags.toList());

    final nextTimes = await NextMaintenanceTime.get(f.id);
    f.nextTaskRecord = RecentMaintenanceDay.calc(nextTimes);

    // 获取最近一天的养护记录

    if (ShareConfig.getShowHistoryNurture()) {
      final lastMaintenanceRecords = await MaintenanceRecord.getLatestDay(f.id);
      f._lastMaintenanceTime = LastMaintenanceTime.calc(lastMaintenanceRecords);
    }

    return f;
  }

  static Future<Flower> create(
      {required String name,
      String? avatar,
      List<TagInfo>? tags,
      FlowerMonthlyCycles? monthlyCycles,
      int? arrivalTime}) async {
    final stringTransform = RStringTransform();
    final normalizedName = await stringTransform.transformString(name);

    final db = await DB.get();
    final createTime = (DateTime.now().millisecondsSinceEpoch / 1000).round();
    final flowerId = await db.sqlite.insert(tableName, {
      'name': name,
      'avatar': avatar,
      'createTime': createTime,
      'arrivalTime': arrivalTime,
      'normalizedName': normalizedName,
    });

    if (tags != null) {
      await TagInfoMapping.create(flowerId, tags);
    }
    if (monthlyCycles != null) {
      final monthlyRepo = MonthlyNurtureCycleRepository();
      await monthlyRepo.saveFlowerMonthlyCycles(flowerId, monthlyCycles);
    }

    final flower = Flower._internal(
      id: flowerId,
      name: name,
      avatar: avatar,
      createTime: createTime,
      tags: tags,
      arrivalTime: arrivalTime,
      normalizedName: normalizedName,
    );

    final nextTimes = await NextMaintenanceTime.createFromFlower(flower, monthlyCycles);
    flower.nextTaskRecord = RecentMaintenanceDay.calc(nextTimes);

    await (await NotificationManger.instance).update(flower.id, null, flower.nextTaskRecord?.maintenanceTime);

    return flower;
  }

  Future<int> update(
      {String? name,
      String? avatar,
      List<TagInfo>? tags,
      FlowerMonthlyCycles? monthlyCycles,
      int? arrivalTime}) async {
    final updateMap = <String, dynamic>{};

    if (name != null) {
      updateMap['name'] = name;
      this.name = name;

      final stringTransform = RStringTransform();
      final normalizedName = await stringTransform.transformString(name);
      updateMap['normalizedName'] = normalizedName;
      _normalizedName = normalizedName;
    }
    if (avatar != null && avatar != _avatar) {
      await _deleteAvatarIfOnlySelf();
      updateMap['avatar'] = avatar;
      _avatar = avatar;
    }
    if (arrivalTime != null && arrivalTime != _arrivalTime) {
      updateMap['arrivalTime'] = arrivalTime;
      _arrivalTime = arrivalTime;
    }

    final int ret;
    if (updateMap.isNotEmpty) {
      final db = await DB.get();
      ret = await db.sqlite.update(tableName, updateMap, where: 'id = ?', whereArgs: [id]);
    } else {
      // 默认成功，影响 1 行
      ret = 1;
    }

    if (!const ListEquality().equals(tags, _tags)) {
      final newTags = tags ?? [];
      _tags.clear();
      _tags.addAll(newTags);

      await TagInfoMapping.update(id, newTags);
    }

    if (monthlyCycles != null) {
      final monthlyRepo = MonthlyNurtureCycleRepository();
      await monthlyRepo.saveFlowerMonthlyCycles(id, monthlyCycles);
    }

    await recalcTaskRecord();
    return ret;
  }

  Future<bool> recalcTaskRecord({bool notify = true}) async {
    final nextTimes = await NextMaintenanceTime.calc(this);
    final newTaskTime = RecentMaintenanceDay.calc(nextTimes);

    if (nextTaskRecord?.maintenanceTime != newTaskTime?.maintenanceTime) {
      await (await NotificationManger.instance)
          .update(id, nextTaskRecord?.maintenanceTime, newTaskTime?.maintenanceTime);
    }

    nextTaskRecord = newTaskTime;

    if (ShareConfig.getShowHistoryNurture()) {
      final lastMaintenanceRecords = await MaintenanceRecord.getLatestDay(id);
      _lastMaintenanceTime = LastMaintenanceTime.calc(lastMaintenanceRecords);
    } else {
      _lastMaintenanceTime = null;
    }

    if (notify) {
      notifyListeners();
    }

    return true;
  }

  Future<void> delete() async {
    final db = await DB.get();

    final monthlyRepo = MonthlyNurtureCycleRepository();
    await monthlyRepo.deleteFlowerMonthlyCycles(id);

    // 删除养护时间、通知
    await recalcTaskRecord(notify: false);
    await MaintenanceRecord.deleteByFlower(id);
    await PhotoRecord.deleteByFlowerId(id);
    await FlowerTimeline.deleteByFlowerId(id);
    await TagInfoMapping.deleteByFlower(id);
    await FlowerNotes.delete(id);

    await _deleteAvatarIfOnlySelf();

    await db.sqlite.delete(tableName, where: 'id = ?', whereArgs: [id]);

    dispose();
  }

  Future<void> _deleteAvatarIfOnlySelf() async {
    if (avatar == null) {
      return;
    }

    final refCount = await _countSameAvatarFile();
    if (refCount > 1) {
      return;
    }

    final avatarFile = File(avatar!);
    if (await avatarFile.exists()) {
      await avatarFile.delete();
    }
  }

  Future<int> _countSameAvatarFile() async {
    final db = await DB.get();

    return Sqflite.firstIntValue(
            await db.sqlite.rawQuery('select count(avatar) from $tableName where avatar = ?', [_avatar])) ??
        0;
  }

  static Future<Iterable<Flower>> searchFlowers(String name) async {
    if (name.isEmpty) {
      return const Iterable<Flower>.empty();
    }

    final db = await DB.get();

    List<Map<String, dynamic>> result =
        await db.sqlite.query(tableName, where: "name like ? and archiveTime is NULL", whereArgs: ["%$name%"]);

    List<Flower> flowers = [];
    for (final row in result) {
      flowers.add(await Flower.createFromDBRow(row));
    }
    return flowers;
  }

  static Future<int> count() async {
    final db = await DB.get();

    return Sqflite.firstIntValue(
            await db.sqlite.rawQuery('select count(id) from $tableName where archiveTime is NULL')) ??
        0;
  }

  Future<void> archive(int time, {String? reason}) async {
    final db = await DB.get();

    await db.sqlite.update(
      tableName,
      {"archiveTime": time, "archiveReason": reason},
      where: 'id = ?',
      whereArgs: [id],
    );

    // 删除通知
    await (await NotificationManger.instance).update(id, nextTaskRecord?.maintenanceTime, null);
  }

  Future<void> unarchive() async {
    final db = await DB.get();

    await db.sqlite.update(tableName, {"archiveTime": null}, where: 'id = ?', whereArgs: [id]);

    // 增加通知
    await (await NotificationManger.instance).update(id, null, nextTaskRecord?.maintenanceTime);
  }

  static Future<List<Flower>> getArchives() async {
    final db = await DB.get();
    List<Map<String, dynamic>> ret = await db.sqlite.query(Flower.tableName, where: "archiveTime is not NULL");

    List<Flower> flowers = [];
    for (final row in ret) {
      flowers.add(await Flower.createFromDBRow(row));
    }
    return flowers;
  }

  static Future<List<Flower>> getAllFlowers() async {
    final db = await DB.get();
    List<Map<String, dynamic>> result = await db.sqlite.query(
      Flower.tableName,
      where: "archiveTime is NULL",
    );

    List<Flower> flowers = [];
    for (final row in result) {
      flowers.add(await Flower.createFromDBRow(row));
    }
    return flowers;
  }

  String? get avatar {
    if (_avatar == null) {
      return null;
    }

    final dir = Global.avatarDir;
    return '$dir/$_avatar';
  }

  List<TagInfo> get tags => List.unmodifiable(_tags);

  int get dayOfCreate {
    final flowerTime = arrivalTime ?? createTime;
    final createDatetime = DateTime.fromMillisecondsSinceEpoch(flowerTime * 1000);
    final now = DateTime.now();

    // 过 24 点算一天
    final createDatetimeZero = DateTime(createDatetime.year, createDatetime.month, createDatetime.day);
    final nowZero = DateTime(now.year, now.month, now.day);

    return nowZero.difference(createDatetimeZero).inDays;
  }

  int? get arrivalTime {
    return _arrivalTime;
  }

  int? get archiveTime {
    return _archiveTime;
  }

  String? get archiveReason {
    return _archiveReason;
  }

  static String getAvatarFullPath(String avatar) {
    final dir = Global.avatarDir;
    return '$dir/$avatar';
  }

  String? get normalizedName {
    return _normalizedName;
  }

  LastMaintenanceTime? get lastMaintenanceTime => _lastMaintenanceTime;

  RecentMaintenanceDay? get nextTask => nextTaskRecord;
}
