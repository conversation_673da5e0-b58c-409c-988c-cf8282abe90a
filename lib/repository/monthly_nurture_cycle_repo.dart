import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flower_timemachine/common/global.dart';
import 'package:flower_timemachine/models/flower_monthly_nurture_cycle.dart';
import 'package:flower_timemachine/types/monthly_cycle_data.dart';
import 'package:flower_timemachine/types/flower_monthly_cycles.dart';

class MonthlyNurtureCycleRepository {
  // 查询某个植物的所有养护类型的月份周期
  static Future<FlowerMonthlyCycles> getFlowerMonthlyCycles(int flowerId) async {
    final records = await FlowerMonthlyNurtureCycle.getByFlower(flowerId);

    final ret = FlowerMonthlyCycles();

    for (final record in records) {
      ret.setCycleForTypeAndMonth(record.typeId, record.month, record.cycle);
    }

    return ret;
  }

  // 查询某个植物的某种养护类型的月份周期
  static Future<MonthlyCycleData> getFlowerTypeMonthlyData(int flowerId, int typeId) async {
    final records = await FlowerMonthlyNurtureCycle.getByFlowerAndType(flowerId, typeId);

    final ret = MonthlyCycleData.createDefault();

    for (final record in records) {
      ret.setCycleForMonth(record.month, record.cycle);
    }

    return ret;
  }

  // 保存某个植物的所有养护类型的月份周期
  static Future<void> saveFlowerMonthlyCycles(int flowerId, FlowerMonthlyCycles flowerCycles) async {
    await _saveFlowerMonthlyCyclesBatch(flowerId, flowerCycles, true);
  }

  // 批量保存数据的内部方法
  static Future<void> _saveFlowerMonthlyCyclesBatch(int flowerId, FlowerMonthlyCycles flowerCycles, bool deleteExisting) async {
    final db = await DB.get();
    final batch = db.sqlite.batch();

    // 如果需要，先删除现有数据
    if (deleteExisting) {
      batch.delete(FlowerMonthlyNurtureCycle.tableName, where: 'flower_id = ?', whereArgs: [flowerId]);
    }

    // 一次性批量插入所有数据
    for (final entry in flowerCycles.typesCycles.entries) {
      final typeId = entry.key;
      final cycleData = entry.value;

      for (int month = 1; month <= 12; month++) {
        final cycle = cycleData.cycles[month - 1];
        batch.insert(FlowerMonthlyNurtureCycle.tableName, {
          'flower_id': flowerId,
          'type_id': typeId,
          'month': month,
          'cycle': cycle,
        });
      }
    }

    await batch.commit(noResult: true);
  }

  // 保存某个植物的某种养护类型的月份周期
  static Future<void> saveFlowerTypeMonthlyData(int flowerId, int typeId, MonthlyCycleData cycleData) async {
    final db = await DB.get();
    final batch = db.sqlite.batch();

    // 删除该植物该养护类型的现有数据
    batch.delete(
      FlowerMonthlyNurtureCycle.tableName,
      where: 'flower_id = ? AND type_id = ?',
      whereArgs: [flowerId, typeId],
    );

    // 批量插入新数据
    for (int month = 1; month <= 12; month++) {
      final cycle = cycleData.cycles[month - 1];
      batch.insert(FlowerMonthlyNurtureCycle.tableName, {
        'flower_id': flowerId,
        'type_id': typeId,
        'month': month,
        'cycle': cycle,
      });
    }

    await batch.commit(noResult: true);
  }

  // 删除某个植物的所有月份周期数据
  static Future<void> deleteFlowerMonthlyCycles(int flowerId) async {
    await FlowerMonthlyNurtureCycle.deleteByFlower(flowerId);
  }

  // 删除某个养护类型的所有月份周期数据
  static Future<void> deleteNurtureTypeMonthlyCycles(int nurtureTypeId) async {
    await FlowerMonthlyNurtureCycle.deleteByNurtureType(nurtureTypeId);
  }

  // 从旧的养护周期数据迁移到新的月份周期数据
  static Future<void> migrateFromLegacyCycles(int flowerId, Map<int, int> legacyCycles) async {
    final flowerCycles = FlowerMonthlyCycles.fromLegacyCycles(legacyCycles);
    await saveFlowerMonthlyCycles(flowerId, flowerCycles);
  }
}

// Riverpod Provider
final monthlyNurtureCycleRepositoryProvider = Provider<MonthlyNurtureCycleRepository>((ref) {
  return MonthlyNurtureCycleRepository();
});
