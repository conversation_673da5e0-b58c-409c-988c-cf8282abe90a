import 'package:flower_timemachine/types/monthly_cycle_data.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flower_timemachine/controller/nurture_types_controller.dart';

class FlowerMonthlyCycles {
  Map<int, MonthlyCycleData> typesCycles = {}; // 养护类型ID -> 月份周期数据

  FlowerMonthlyCycles();

  // 获取某个养护类型的月份周期数据
  MonthlyCycleData? getCycleDataForType(int typeId) {
    return typesCycles[typeId];
  }

  // 设置某个养护类型的月份周期数据
  void setCycleDataForType(int typeId, MonthlyCycleData cycleData) {
    typesCycles[typeId] = cycleData;
  }

  // 获取某个养护类型某月的周期
  int getCycleForTypeAndMonth(int typeId, int month) {
    final cycleData = typesCycles[typeId];
    if (cycleData == null) {
      return 0; // 继承
    }
    return cycleData.getCycleForMonth(month);
  }

  // 设置某个养护类型某月的周期
  void setCycleForTypeAndMonth(int typeId, int month, int cycle) {
    final currentCycleData = typesCycles[typeId] ?? MonthlyCycleData.createDefault();
    currentCycleData.setCycleForMonth(month, cycle);
    typesCycles[typeId] = currentCycleData;
  }

  // 获取某个养护类型某月的实际周期（处理继承逻辑）
  int getEffectiveCycleForTypeAndMonth(int typeId, int month, NurtureType nurtureType) {
    final cycleData = typesCycles[typeId];
    if (cycleData == null) {
      return nurtureType.defaultCycle;
    }
    return cycleData.getEffectiveCycleForMonth(month, nurtureType.defaultCycle);
  }

  // 移除某个养护类型的数据
  void removeType(int typeId) {
    typesCycles.remove(typeId);
  }

  // 创建空的花卉月份周期数据
  static FlowerMonthlyCycles createEmpty() {
    return FlowerMonthlyCycles();
  }

  // 复制对象
  FlowerMonthlyCycles copy() {
    final newFlowerCycles = FlowerMonthlyCycles();
    for (final entry in typesCycles.entries) {
      newFlowerCycles.typesCycles[entry.key] = entry.value.copy();
    }
    return newFlowerCycles;
  }

  // 从旧的养护周期数据创建（迁移用）
  static FlowerMonthlyCycles fromLegacyCycles(Map<int, int> legacyCycles) {
    final flowerCycles = FlowerMonthlyCycles();

    for (final entry in legacyCycles.entries) {
      final typeId = entry.key;
      final cycle = entry.value;

      // 旧数据中 0 表示未设置，转换为新数据中的 -1
      final newCycle = cycle == 0 ? -1 : cycle;
      flowerCycles.typesCycles[typeId] = MonthlyCycleData.fromSingleCycle(newCycle);
    }

    return flowerCycles;
  }
}
